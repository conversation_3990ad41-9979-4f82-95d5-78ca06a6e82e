"use client"

import { Heart, Globe } from "lucide-react"
import Image from "next/image"
import { useLanguage } from "@/lib/i18n/language-context"

export default function AboutPage() {
  const { t, dir } = useLanguage()

  return (
    <div className="flex flex-col" dir={dir}>
      {/* Hero Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter text-foreground sm:text-4xl md:text-5xl">
                {t("aboutPage.title")}
              </h1>
              <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("aboutPage.subtitle")}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* About Content */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter">{t("aboutPage.ourStory")}</h2>
                <p className="text-gray-500 md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                  {t("aboutPage.story1")}
                </p>
                <p className="text-gray-500 md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                  {t("aboutPage.story2")}
                </p>
                <p className="text-gray-500 md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                  {t("aboutPage.story3")}
                </p>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative h-[300px] w-full overflow-hidden rounded-lg">
                <Image src="/placeholder.svg?height=600&width=800" alt="About ICDYA" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter">{t("aboutPage.ourMission")}</h2>
              <p className="max-w-[900px] text-gray-500 md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                {t("aboutPage.mission1")}
              </p>
              <p className="max-w-[900px] text-gray-500 md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                {t("aboutPage.mission2")}
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2">
            <div className="flex flex-col space-y-2 rounded-lg border p-6 shadow-sm bg-white">
              <Heart className="h-10 w-10 text-emerald-600" />
              <h3 className="text-xl font-bold">{t("aboutPage.ourValues")}</h3>
              <ul className="list-disc pl-5 text-gray-500 space-y-1">
                <li>{t("aboutPage.value1")}</li>
                <li>{t("aboutPage.value2")}</li>
                <li>{t("aboutPage.value3")}</li>
                <li>{t("aboutPage.value4")}</li>
                <li>{t("aboutPage.value5")}</li>
              </ul>
            </div>
            <div className="flex flex-col space-y-2 rounded-lg border p-6 shadow-sm bg-white">
              <Globe className="h-10 w-10 text-emerald-600" />
              <h3 className="text-xl font-bold">{t("aboutPage.ourApproach")}</h3>
              <ul className="list-disc pl-5 text-gray-500 space-y-1">
                <li>{t("aboutPage.approach1")}</li>
                <li>{t("aboutPage.approach2")}</li>
                <li>{t("aboutPage.approach3")}</li>
                <li>{t("aboutPage.approach4")}</li>
                <li>{t("aboutPage.approach5")}</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Environmental Commitment */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-white">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
            <div className="flex items-center justify-center">
              <div className="relative h-[300px] w-full overflow-hidden rounded-lg">
                <Image
                  src="/placeholder.svg?height=600&width=800"
                  alt="Environmental Commitment"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter">{t("aboutPage.environmentalCommitment")}</h2>
                <p className="text-gray-500 md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                  {t("aboutPage.environmental1")}
                </p>
                <p className="text-gray-500 md:text-lg/relaxed lg:text-base/relaxed xl:text-lg/relaxed">
                  {t("aboutPage.environmental2")}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
