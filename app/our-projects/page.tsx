"use client"

import { useLanguage } from "@/lib/i18n/language-context"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Image from "next/image"
import { CalendarIcon, Users, Target, Award, CheckCircle } from "lucide-react"

export default function OurProjectsPage() {
  const { t, dir } = useLanguage()

  const projects = [
    {
      id: 1,
      titleKey: "ourProjects.youth.title",
      descriptionKey: "ourProjects.youth.description",
      image: "/placeholder.svg?height=400&width=600",
      categories: ["category.education", "category.leadership"],
      date: "2022",
      participants: 45,
      outcomes: ["ourProjects.youth.outcome1", "ourProjects.youth.outcome2", "ourProjects.youth.outcome3"],
      location: "ourProjects.youth.location",
    },
    {
      id: 2,
      titleKey: "ourProjects.environmental.title",
      descriptionKey: "ourProjects.environmental.description",
      image: "/placeholder.svg?height=400&width=600",
      categories: ["category.environment", "category.community"],
      date: "2021",
      participants: 120,
      outcomes: [
        "ourProjects.environmental.outcome1",
        "ourProjects.environmental.outcome2",
        "ourProjects.environmental.outcome3",
      ],
      location: "ourProjects.environmental.location",
    },
    {
      id: 3,
      titleKey: "ourProjects.cultural.title",
      descriptionKey: "ourProjects.cultural.description",
      image: "/placeholder.svg?height=400&width=600",
      categories: ["category.cultural", "category.international"],
      date: "2021",
      participants: 75,
      outcomes: ["ourProjects.cultural.outcome1", "ourProjects.cultural.outcome2", "ourProjects.cultural.outcome3"],
      location: "ourProjects.cultural.location",
    },
    {
      id: 4,
      titleKey: "ourProjects.vocational.title",
      descriptionKey: "ourProjects.vocational.description",
      image: "/placeholder.svg?height=400&width=600",
      categories: ["category.vocational", "category.training"],
      date: "2020",
      participants: 60,
      outcomes: [
        "ourProjects.vocational.outcome1",
        "ourProjects.vocational.outcome2",
        "ourProjects.vocational.outcome3",
      ],
      location: "ourProjects.vocational.location",
    },
    {
      id: 5,
      titleKey: "ourProjects.digital.title",
      descriptionKey: "ourProjects.digital.description",
      image: "/placeholder.svg?height=400&width=600",
      categories: ["category.technology", "category.education"],
      date: "2020",
      participants: 90,
      outcomes: ["ourProjects.digital.outcome1", "ourProjects.digital.outcome2", "ourProjects.digital.outcome3"],
      location: "ourProjects.digital.location",
    },
    {
      id: 6,
      titleKey: "ourProjects.community.title",
      descriptionKey: "ourProjects.community.description",
      image: "/placeholder.svg?height=400&width=600",
      categories: ["category.community", "category.development"],
      date: "2019",
      participants: 150,
      outcomes: ["ourProjects.community.outcome1", "ourProjects.community.outcome2", "ourProjects.community.outcome3"],
      location: "ourProjects.community.location",
    },
  ]

  // Group projects by year
  const projectsByYear = projects.reduce(
    (acc, project) => {
      if (!acc[project.date]) {
        acc[project.date] = []
      }
      acc[project.date].push(project)
      return acc
    },
    {} as Record<string, typeof projects>,
  )

  // Sort years in descending order
  const years = Object.keys(projectsByYear).sort((a, b) => Number.parseInt(b) - Number.parseInt(a))

  return (
    <div className="flex flex-col" dir={dir}>
      {/* Hero Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter text-foreground sm:text-4xl md:text-5xl">
                {t("ourProjects.title")}
              </h1>
              <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("ourProjects.subtitle")}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
        <div className="container px-4 md:px-6">
          <Tabs defaultValue={years[0]} className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList>
                {years.map((year) => (
                  <TabsTrigger key={year} value={year}>
                    {year}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            {years.map((year) => (
              <TabsContent key={year} value={year} className="space-y-8">
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {projectsByYear[year].map((project) => (
                    <Card key={project.id} className="overflow-hidden flex flex-col h-full">
                      <div className="relative h-[200px] w-full">
                        <Image
                          src={project.image || "/placeholder.svg"}
                          alt={t(project.titleKey)}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <CardHeader>
                        <div className="flex flex-wrap gap-2 mb-2">
                          {project.categories.map((category) => (
                            <Badge key={category} variant="outline" className="bg-accent dark:bg-accent">
                              {t(category)}
                            </Badge>
                          ))}
                        </div>
                        <CardTitle>{t(project.titleKey)}</CardTitle>
                        <CardDescription className="flex items-center gap-1 mt-1">
                          <CalendarIcon className="h-4 w-4" /> {project.date}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex-1 flex flex-col">
                        <p className="text-muted-foreground mb-4">{t(project.descriptionKey)}</p>

                        <div className="mt-auto space-y-4">
                          <div className="flex items-center gap-2">
                            <Users className="h-5 w-5 text-primary" />
                            <span className="text-sm font-medium">
                              {t("ourProjects.participants")}: {project.participants}
                            </span>
                          </div>

                          <div className="flex items-center gap-2">
                            <Target className="h-5 w-5 text-primary" />
                            <span className="text-sm font-medium">
                              {t("ourProjects.location")}: {t(project.location)}
                            </span>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Award className="h-5 w-5 text-primary" />
                              <span className="text-sm font-medium">{t("ourProjects.outcomes")}</span>
                            </div>
                            <ul className="space-y-1 pl-7">
                              {project.outcomes.map((outcome, index) => (
                                <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                                  <CheckCircle className="h-4 w-4 text-primary shrink-0 mt-0.5" />
                                  <span>{t(outcome)}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </section>

      {/* Impact Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter">{t("ourProjects.impact.title")}</h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("ourProjects.impact.subtitle")}
              </p>
            </div>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-2">
                  <Users className="h-12 w-12 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">540+</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t("ourProjects.impact.participants")}</p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-2">
                  <Award className="h-12 w-12 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">15+</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t("ourProjects.impact.projects")}</p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-2">
                  <Target className="h-12 w-12 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">8+</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t("ourProjects.impact.countries")}</p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-2">
                  <CalendarIcon className="h-12 w-12 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">5+</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{t("ourProjects.impact.years")}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
