"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { TeamSection } from "@/components/team-section"
import { useLanguage } from "@/lib/i18n/language-context"

export default function TeamPage() {
  const { t, dir } = useLanguage()

  return (
    <div className="flex flex-col" dir={dir}>
      {/* Hero Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter text-foreground sm:text-4xl md:text-5xl">
                {t("teamPage.title")}
              </h1>
              <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("teamPage.subtitle")}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Members */}
      <TeamSection fullPage={true} />

      {/* Join Our Team */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter">{t("teamPage.joinOurTeam")}</h2>
              <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("teamPage.joinDescription")}
              </p>
            </div>
            <div className="w-full max-w-sm space-y-2">
              <Card>
                <CardHeader>
                  <CardTitle>{t("teamPage.volunteerOpportunities")}</CardTitle>
                  <CardDescription>{t("teamPage.volunteerDescription")}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">{t("teamPage.volunteerText")}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
