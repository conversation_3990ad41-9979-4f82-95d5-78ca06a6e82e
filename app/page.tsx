"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Globe, Users, Leaf, BookOpen } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { TeamSection } from "@/components/team-section"
import { useLanguage } from "@/lib/i18n/language-context"

export default function Home() {
  const { t, dir } = useLanguage()

  return (
    <div className="flex flex-col" dir={dir}>
      {/* Hero Section */}
      <section className="relative w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-b from-emerald-50 to-background dark:from-accent dark:to-background">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter text-foreground sm:text-5xl xl:text-6xl/none">
                  {t("home.title")}
                </h1>
                <p className="max-w-[600px] text-primary md:text-xl font-semibold">{t("home.acronym")}</p>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">{t("home.subtitle")}</p>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Link href="/about">
                  <Button className="bg-primary hover:bg-primary/90">
                    {t("home.learnMore")} <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button variant="outline">{t("home.contactUs")}</Button>
                </Link>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative h-[300px] w-[300px] md:h-[400px] md:w-[400px] lg:h-[500px] lg:w-[500px]">
                <Image
                  src="/placeholder.svg?height=500&width=500"
                  alt="Youth Association"
                  fill
                  className="object-cover rounded-lg shadow-xl"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{t("mission.title")}</h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("mission.description")}
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Globe className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.globalCooperation")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.globalCooperationDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Users className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.youthEmpowerment")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.youthEmpowermentDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Leaf className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.sustainability")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.sustainabilityDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <BookOpen className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.education")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.educationDesc")}</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50 dark:bg-accent">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{t("about.title")}</h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  {t("about.description1")}
                </p>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  {t("about.description2")}
                </p>
              </div>
              <div>
                <Link href="/about">
                  <Button className="bg-primary hover:bg-primary/90">
                    {t("about.learnMoreAboutUs")} <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative h-[300px] w-full overflow-hidden rounded-lg">
                <Image src="/placeholder.svg?height=600&width=800" alt="About ICDYA" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <TeamSection />
    </div>
  )
}
