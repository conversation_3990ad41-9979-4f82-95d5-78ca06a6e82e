"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Globe, Users, Leaf, BookOpen } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { TeamSection } from "@/components/team-section"
import { useLanguage } from "@/lib/i18n/language-context"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"

export default function Home() {
  const { t, dir } = useLanguage()

  // Sample images for the slider - you can replace these with your actual images
  const sliderImages = [
    {
      src: "/placeholder.svg?height=600&width=1200&text=Youth+Empowerment",
      alt: "Youth Empowerment",
      title: "Empowering Youth Worldwide",
      description: "Building bridges between cultures and communities"
    },
    {
      src: "/placeholder.svg?height=600&width=1200&text=Global+Cooperation",
      alt: "Global Cooperation",
      title: "Global Cooperation",
      description: "Working together for a sustainable future"
    },
    {
      src: "/placeholder.svg?height=600&width=1200&text=Education+Programs",
      alt: "Education Programs",
      title: "Education & Development",
      description: "Providing opportunities for growth and learning"
    },
    {
      src: "/placeholder.svg?height=600&width=1200&text=Community+Impact",
      alt: "Community Impact",
      title: "Community Impact",
      description: "Making a difference in communities around the world"
    }
  ]

  return (
    <div className="flex flex-col" dir={dir}>
      {/* Logo Section - Bigger and Centered */}
      {/* <section className="w-full py-8 md:py-12 bg-background">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="relative h-32 w-32 md:h-48 md:w-48 lg:h-64 lg:w-64">
              <Image
                src="/images/icdya-logo.png"
                alt="ICDYA Logo"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="text-center space-y-2">
              <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold text-foreground">
                {t("home.title")}
              </h1>
              <p className="text-lg md:text-xl lg:text-2xl text-primary font-semibold">
                {t("home.acronym")}
              </p>
            </div>
          </div>
        </div>
      </section> */}

      {/* Image Slider Section */}
      <section className="w-full py-8 md:py-12 bg-gradient-to-b from-emerald-50 to-background dark:from-accent dark:to-background">
        <div className="container px-4 md:px-6">
          <Carousel
            plugins={[
              Autoplay({
                delay: 4000,
              }),
            ]}
            className="w-full max-w-6xl mx-auto"
          >
            <CarouselContent>
              {sliderImages.map((image, index) => (
                <CarouselItem key={index}>
                  <div className="relative h-[400px] md:h-[500px] lg:h-[600px] w-full overflow-hidden rounded-lg">
                    <Image
                      src={image.src}
                      alt={image.alt}
                      fill
                      className="object-cover"
                      priority={index === 0}
                    />
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <div className="text-center text-white space-y-4 px-4">
                        <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold">
                          {image.title}
                        </h2>
                        <p className="text-lg md:text-xl lg:text-2xl max-w-2xl">
                          {image.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-4" />
            <CarouselNext className="right-4" />
          </Carousel>
        </div>
      </section>

      {/* Hero Content Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            <div className="space-y-4">
              <p className="max-w-[800px] text-muted-foreground md:text-xl lg:text-2xl">
                {t("home.subtitle")}
              </p>
            </div>
            <div className="flex flex-col gap-4 min-[400px]:flex-row">
              <Link href="/about">
                <Button size="lg" className="bg-primary hover:bg-primary/90">
                  {t("home.learnMore")} <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/contact">
                <Button size="lg" variant="outline">{t("home.contactUs")}</Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{t("mission.title")}</h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("mission.description")}
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Globe className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.globalCooperation")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.globalCooperationDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Users className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.youthEmpowerment")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.youthEmpowermentDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Leaf className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.sustainability")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.sustainabilityDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <BookOpen className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.education")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.educationDesc")}</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50 dark:bg-accent">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{t("about.title")}</h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  {t("about.description1")}
                </p>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  {t("about.description2")}
                </p>
              </div>
              <div>
                <Link href="/about">
                  <Button className="bg-primary hover:bg-primary/90">
                    {t("about.learnMoreAboutUs")} <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative h-[300px] w-full overflow-hidden rounded-lg">
                <Image src="/placeholder.svg?height=600&width=800" alt="About ICDYA" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <TeamSection />
    </div>
  )
}
