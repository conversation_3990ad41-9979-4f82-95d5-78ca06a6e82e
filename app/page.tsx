"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Globe, Users, Leaf, BookOpen } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { TeamSection } from "@/components/team-section"
import { useLanguage } from "@/lib/i18n/language-context"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"
import { useState, useEffect, useRef } from "react"
import type { CarouselApi } from "@/components/ui/carousel"

export default function Home() {
  const { t, dir } = useLanguage()
  const [currentSlide, setCurrentSlide] = useState(0)
  const [api, setApi] = useState<CarouselApi>()

  useEffect(() => {
    if (!api) {
      return
    }

    setCurrentSlide(api.selectedScrollSnap())

    api.on("select", () => {
      setCurrentSlide(api.selectedScrollSnap())
    })
  }, [api])

  // Your actual images from the public/images folder
  const sliderImages = [
    {
      src: "/images/outside_icdya.png",
      alt: "ICDYA Building",
      title: "Our Headquarters",
      description: "The ICDYA building where we coordinate our global youth initiatives"
    },
    {
      src: "/images/calisma_grubu_ayakta.png",
      alt: "Working Group Standing",
      title: "Our Team in Action",
      description: "Dedicated team members working together for youth empowerment"
    },
    {
      src: "/images/resim_ekibi_ile_cocuklar.png",
      alt: "Art Team with Children",
      title: "Art Education Programs",
      description: "Engaging children through creative art and educational activities"
    },
    {
      src: "/images/sanliurfa_bilim_merkezi.JPG",
      alt: "Sanliurfa Science Center",
      title: "Science Education",
      description: "Promoting STEM education and scientific learning"
    },
    {
      src: "/images/seminer_salonu.png",
      alt: "Seminar Hall",
      title: "Educational Seminars",
      description: "Hosting educational seminars and workshops for youth development"
    },
    {
      src: "/images/calisma_ekibi_masada.png",
      alt: "Team Meeting",
      title: "Strategic Planning",
      description: "Our team collaborating on strategic initiatives and planning"
    }
  ]

  return (
    <div className="flex flex-col" dir={dir}>
      {/* Logo Section - Bigger and Centered */}
      {/* <section className="w-full py-8 md:py-12 bg-background">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="relative h-32 w-32 md:h-48 md:w-48 lg:h-64 lg:w-64">
              <Image
                src="/images/icdya-logo.png"
                alt="ICDYA Logo"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="text-center space-y-2">
              <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold text-foreground">
                {t("home.title")}
              </h1>
              <p className="text-lg md:text-xl lg:text-2xl text-primary font-semibold">
                {t("home.acronym")}
              </p>
            </div>
          </div>
        </div>
      </section> */}

      {/* Image Slider Section */}
      <section className="w-full py-8 md:py-12 bg-gradient-to-b from-emerald-50 to-background dark:from-accent dark:to-background">
        <div className="container px-4 md:px-6">
          <div className="w-full max-w-6xl mx-auto space-y-6">
            <Carousel
              plugins={[
                Autoplay({
                  delay: 5000,
                }),
              ]}
              className="w-full"
              setApi={setApi}
            >
              <CarouselContent>
                {sliderImages.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative h-[400px] md:h-[500px] lg:h-[600px] w-full overflow-hidden rounded-lg">
                      <Image
                        src={image.src}
                        alt={image.alt}
                        fill
                        className="object-cover"
                        priority={index === 0}
                      />
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                        <div className="text-center text-white space-y-4 px-4">
                          <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold">
                            {image.title}
                          </h2>
                          <p className="text-lg md:text-xl lg:text-2xl max-w-2xl">
                            {image.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="left-4" />
              <CarouselNext className="right-4" />
            </Carousel>

            {/* Thumbnail Preview Section */}
            <div className="flex justify-center">
              <div className="grid grid-cols-3 md:grid-cols-6 gap-2 md:gap-4 max-w-4xl">
                {sliderImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`relative h-16 md:h-20 lg:h-24 w-full overflow-hidden rounded-md transition-all duration-300 ${
                      currentSlide === index
                        ? 'ring-2 ring-primary ring-offset-2 scale-105'
                        : 'opacity-70 hover:opacity-100 hover:scale-105'
                    }`}
                  >
                    <Image
                      src={image.src}
                      alt={image.alt}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20 hover:bg-black/10 transition-colors" />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Hero Content Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            <div className="space-y-4">
              <p className="max-w-[800px] text-muted-foreground md:text-xl lg:text-2xl">
                {t("home.subtitle")}
              </p>
            </div>
            <div className="flex flex-col gap-4 min-[400px]:flex-row">
              <Link href="/about">
                <Button size="lg" className="bg-primary hover:bg-primary/90">
                  {t("home.learnMore")} <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/contact">
                <Button size="lg" variant="outline">{t("home.contactUs")}</Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{t("mission.title")}</h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                {t("mission.description")}
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Globe className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.globalCooperation")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.globalCooperationDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Users className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.youthEmpowerment")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.youthEmpowermentDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <Leaf className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.sustainability")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.sustainabilityDesc")}</p>
            </div>
            <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
              <BookOpen className="h-12 w-12 text-primary" />
              <h3 className="text-xl font-bold">{t("mission.education")}</h3>
              <p className="text-sm text-muted-foreground text-center">{t("mission.educationDesc")}</p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-emerald-50 dark:bg-accent">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{t("about.title")}</h2>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  {t("about.description1")}
                </p>
                <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  {t("about.description2")}
                </p>
              </div>
              <div>
                <Link href="/about">
                  <Button className="bg-primary hover:bg-primary/90">
                    {t("about.learnMoreAboutUs")} <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="relative h-[300px] w-full overflow-hidden rounded-lg">
                <Image src="/placeholder.svg?height=600&width=800" alt="About ICDYA" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <TeamSection />
    </div>
  )
}
