"use client"

import * as React from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { ModeToggle } from "./mode-toggle"
import { She<PERSON>, <PERSON>et<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"
import { usePathname } from "next/navigation"
import { useLanguage } from "@/lib/i18n/language-context"
import { LanguageSwitcher } from "./language-switcher"
import Image from "next/image"

export default function Navbar() {
  const [isOpen, setIsOpen] = React.useState(false)
  const pathname = usePathname()
  const { t, dir } = useLanguage()

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        <div className="mr-4 hidden md:flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <div className="relative h-10 w-10">
              <Image src="/images/icdya-logo.png" alt="ICDYA Logo" width={40} height={40} />
            </div>
          </Link>
          <NavigationMenu dir={dir}>
            <NavigationMenuList>
              <NavigationMenuItem>
                <Link href="/" legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>{t("nav.home")}</NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href="/about" legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>{t("nav.about")}</NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href="/our-projects" legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    {t("nav.ourProjects")}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href="/team" legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>{t("nav.team")}</NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href="/contact" legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>{t("nav.contact")}</NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        </div>
        <div className="flex md:hidden">
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="mr-2">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left">
              <Link href="/" className="flex items-center" onClick={() => setIsOpen(false)}>
                <div className="relative h-8 w-8 mr-2">
                  <Image src="/images/icdya-logo.png" alt="ICDYA Logo" width={32} height={32} />
                </div>
                <span className="text-xl font-bold">ICDYA</span>
              </Link>
              <div className="mt-8 flex flex-col space-y-4">
                <Link
                  href="/"
                  className={cn(
                    "text-lg font-medium transition-colors",
                    pathname === "/" ? "text-foreground" : "text-foreground/60",
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  {t("nav.home")}
                </Link>
                <Link
                  href="/about"
                  className={cn(
                    "text-lg font-medium transition-colors",
                    pathname === "/about" ? "text-foreground" : "text-foreground/60",
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  {t("nav.about")}
                </Link>
                <Link
                  href="/team"
                  className={cn(
                    "text-lg font-medium transition-colors",
                    pathname === "/team" ? "text-foreground" : "text-foreground/60",
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  {t("nav.team")}
                </Link>
                <Link
                  href="/our-projects"
                  className={cn(
                    "text-lg font-medium transition-colors",
                    pathname === "/our-projects" ? "text-foreground" : "text-foreground/60",
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  {t("nav.ourProjects")}
                </Link>
                <Link
                  href="/contact"
                  className={cn(
                    "text-lg font-medium transition-colors",
                    pathname === "/contact" ? "text-foreground" : "text-foreground/60",
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  {t("nav.contact")}
                </Link>
              </div>
            </SheetContent>
          </Sheet>
          <Link href="/" className="flex items-center">
            <Image src="/images/icdya-logo.png" alt="ICDYA Logo" width={32} height={32} className="h-8 w-auto" />
          </Link>
        </div>
        <div className="flex flex-1 items-center justify-end space-x-4">
          <nav className="flex items-center space-x-2">
            <LanguageSwitcher />
            <ModeToggle />
            <Link href="/contact">
              <Button className="hidden md:flex bg-emerald-600 hover:bg-emerald-700">{t("nav.getInvolved")}</Button>
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}
