"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/lib/i18n/language-context"

export default function Footer() {
  const { t } = useLanguage()

  return (
    <footer className="w-full border-t bg-background">
      <div className="container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            &copy; {new Date().getFullYear()} {t("home.title")}. {t("footer.rights")}
          </p>
        </div>
        <div className="flex gap-4">
          <Link href="/about">
            <Button variant="ghost" size="sm">
              {t("nav.about")}
            </Button>
          </Link>
          <Link href="/our-projects">
            <Button variant="ghost" size="sm">
              {t("nav.ourProjects")}
            </Button>
          </Link>
          <Link href="/team">
            <Button variant="ghost" size="sm">
              {t("nav.team")}
            </Button>
          </Link>
          <Link href="/contact">
            <Button variant="ghost" size="sm">
              {t("nav.contact")}
            </Button>
          </Link>
        </div>
      </div>
    </footer>
  )
}
