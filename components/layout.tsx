import React, { ReactNode } from 'react';
import Head from 'next/head';

interface LayoutProps {
  children: ReactNode;
  title?: string;
}

const Layout = ({ children, title = 'ICDYA - Islamic Center for Developing Youth and Adults' }: LayoutProps) => {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta charSet="utf-8" />
        <meta name="viewport" content="initial-scale=1.0, width=device-width" />
      </Head>
      <header>
        {/* Your header/navigation would go here */}
      </header>
      <main>{children}</main>
      <footer>
        {/* Your footer would go here */}
      </footer>
    </>
  );
};

export default Layout;
