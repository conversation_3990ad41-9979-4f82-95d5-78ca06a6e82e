"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useLanguage } from "@/lib/i18n/language-context"

interface TeamSectionProps {
  fullPage?: boolean
}

export function TeamSection({ fullPage = false }: TeamSectionProps) {
  const { t, dir } = useLanguage()

  const teamMembers = [
    {
      nameKey: "team.muhammed.name",
      roleKey: "team.muhammed.role",
      bioKey: "team.muhammed.bio",
      avatar: "/placeholder.svg?height=200&width=200",
      initials: "MC",
    },
    {
      nameKey: "team.mohammed.name",
      roleKey: "team.mohammed.role",
      bioKey: "team.mohammed.bio",
      avatar: "/placeholder.svg?height=200&width=200",
      initials: "MNA",
    },
  ]

  if (fullPage) {
    return (
      <section className="w-full py-12 md:py-24 lg:py-32 bg-background" dir={dir}>
        <div className="container px-4 md:px-6">
          <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2">
            {teamMembers.map((member) => (
              <Card key={member.nameKey} className="overflow-hidden">
                <CardHeader className="flex flex-col items-center text-center">
                  <Avatar className="h-24 w-24 mb-4">
                    <AvatarImage src={member.avatar || "/placeholder.svg"} alt={t(member.nameKey)} />
                    <AvatarFallback>{member.initials}</AvatarFallback>
                  </Avatar>
                  <CardTitle>{t(member.nameKey)}</CardTitle>
                  <CardDescription className="text-primary font-medium">{t(member.roleKey)}</CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground">{t(member.bioKey)}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-background" dir={dir}>
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{t("team.title")}</h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              {t("team.subtitle")}
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2">
          {teamMembers.map((member) => (
            <Card key={member.nameKey} className="overflow-hidden">
              <CardHeader className="flex flex-col items-center text-center">
                <Avatar className="h-24 w-24 mb-4">
                  <AvatarImage src={member.avatar || "/placeholder.svg"} alt={t(member.nameKey)} />
                  <AvatarFallback>{member.initials}</AvatarFallback>
                </Avatar>
                <CardTitle>{t(member.nameKey)}</CardTitle>
                <CardDescription className="text-primary font-medium">{t(member.roleKey)}</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground line-clamp-3">{t(member.bioKey)}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
