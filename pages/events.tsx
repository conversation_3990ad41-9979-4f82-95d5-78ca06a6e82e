import Layout from '../components/layout'; 
import EventCarousel from '../components/EventCarousel';
import TestimonialCarousel from '../components/TestimonialCarousel';
import AnimatedText from '../components/AnimatedText';
import { motion } from 'framer-motion';

// Sample data for events
const events = [
  {
    id: 1,
    title: "Annual Youth Leadership Conference 2023",
    date: "June 15-17, 2023",
    description: "Join us for three days of inspiring workshops, keynote speeches, and networking opportunities designed to empower the next generation of community leaders.",
    image: "/images/events/conference.jpg",
  },
  {
    id: 2,
    title: "Community Service Day",
    date: "August 5, 2023",
    description: "Make a difference in our local community through various service projects. From park cleanups to food drives, there's a way for everyone to contribute.",
    image: "/images/events/service-day.jpg",
  },
  {
    id: 3,
    title: "Cultural Celebration Night",
    date: "September 23, 2023",
    description: "Experience the rich diversity of our community through food, music, dance, and art from around the world. A celebration of our shared humanity.",
    image: "/images/events/cultural-night.jpg",
  }
];

// Sample data for testimonials
const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Youth Program Participant",
    content: "The leadership program completely transformed my perspective on community engagement. I've gained skills that I'll carry with me throughout my career and life.",
    avatar: "/images/testimonials/sarah.jpg",
  },
  {
    id: 2,
    name: "Ahmed Hassan",
    role: "Volunteer Coordinator",
    content: "Working with ICDYA has been one of the most rewarding experiences of my life. The organization truly puts its mission into practice everyday.",
    avatar: "/images/testimonials/ahmed.jpg",
  },
  {
    id: 3,
    name: "Maria Rodriguez",
    role: "Parent",
    content: "I've seen incredible growth in my daughter since she joined the youth programs. She's more confident, engaged with her community, and motivated to make a difference.",
    avatar: "/images/testimonials/maria.jpg",
  },
];

const EventsPage = () => {
  return (
    <Layout>
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                <AnimatedText text="Upcoming Events" className="text-blue-900" />
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                <AnimatedText 
                  text="Join us in our mission to empower communities through diverse and engaging events throughout the year." 
                  once={true}
                />
              </p>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="mb-24"
          >
            <EventCarousel events={events} />
          </motion.div>

          <div className="my-24">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
            >
              <TestimonialCarousel testimonials={testimonials} />
            </motion.div>
          </div>

          <div className="my-24">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="bg-gradient-to-r from-blue-800 to-purple-800 rounded-2xl p-8 md:p-16 text-white text-center"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                <AnimatedText text="Get Involved" type="words" />
              </h2>
              <p className="text-lg md:text-xl mb-8 max-w-xl mx-auto">
                <AnimatedText 
                  text="Don't miss out on our upcoming events! Sign up for our newsletter to stay updated." 
                  type="words"
                  once={true}
                />
              </p>
              
              <motion.div
                className="max-w-md mx-auto"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.5 }}
              >
                <div className="flex flex-col sm:flex-row gap-3">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="flex-grow px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-6 py-3 bg-yellow-500 text-blue-900 font-semibold rounded-lg hover:bg-yellow-400"
                  >
                    Subscribe
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default EventsPage;
