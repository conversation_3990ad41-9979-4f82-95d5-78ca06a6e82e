/** @type {import('next').NextConfig} */
const nextConfig = {
  // ...existing code...
  
  // Add allowed development origins
  experimental: {
    // ...existing code...
    allowedDevOrigins: [
      'http://**************:3000',
      'http://localhost:3000',
    ],
  },
  
  // Increase the timeout for chunk loading to prevent timeout errors
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 60 * 1000,
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 5,
  },
  
  // ...existing code...
}

module.exports = nextConfig